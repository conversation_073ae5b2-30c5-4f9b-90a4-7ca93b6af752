# Database Setup - SQLite with Prisma

This project uses SQLite as the database with Prisma ORM for type-safe database operations.

## Database Structure

### Models
- **User**: Admin, Manager, and Cashier users
- **Category**: Product categories (Beverages, Snacks, etc.)
- **Product**: Inventory items with stock tracking
- **Sale**: Sales transactions
- **SaleItem**: Individual items in a sale
- **StockAdjustment**: Inventory adjustments for stock management

## Available Scripts

```bash
# Generate Prisma client (run after schema changes)
pnpm db:generate

# Push schema changes to database (for development)
pnpm db:push

# Create and run migrations (for production)
pnpm db:migrate

# Open Prisma Studio (database GUI)
pnpm db:studio

# Seed database with sample data
pnpm db:seed

# Reset database (WARNING: deletes all data)
pnpm db:reset
```

## Database Location

The SQLite database file is located at `./dev.db` in the project root.

## Usage Examples

### Using the database utilities

```typescript
import { db } from '@/lib/db'

// Get all products with categories
const products = await db.products.getAll()

// Get product by barcode
const product = await db.products.getByBarcode('1234567890123')

// Get low stock products
const lowStock = await db.products.getLowStock()

// Get today's sales
const todaysSales = await db.sales.getTodaysSales()
```

### Direct Prisma usage

```typescript
import { prisma } from '@/lib/db'

// Create a new product
const product = await prisma.product.create({
  data: {
    name: 'New Product',
    price: 9.99,
    stock: 100,
    categoryId: 'category-id'
  }
})

// Complex queries
const salesReport = await prisma.sale.findMany({
  where: {
    createdAt: {
      gte: new Date('2024-01-01'),
      lt: new Date('2024-02-01')
    }
  },
  include: {
    saleItems: {
      include: {
        product: true
      }
    }
  }
})
```

## API Routes

The following API routes are available:

- `GET /api/products` - Get all products
- `POST /api/products` - Create a new product
- `GET /api/categories` - Get all categories
- `POST /api/categories` - Create a new category

## Environment Variables

Make sure to set the following in your `.env` file:

```env
DATABASE_URL="file:./dev.db"
```

## Schema Changes

When you modify the Prisma schema:

1. Run `pnpm db:generate` to update the Prisma client
2. Run `pnpm db:push` to apply changes to the database (development)
3. Or run `pnpm db:migrate` to create a migration (production)

## Backup and Restore

To backup your SQLite database:
```bash
cp dev.db backup-$(date +%Y%m%d).db
```

To restore from backup:
```bash
cp backup-20240101.db dev.db
```
