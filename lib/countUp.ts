// lib/countUp.ts

type CountUpOptions = {
  duration?: number; // total animation time in ms
  start?: number; // default: 0
  end: number;
  onUpdate?: (value: number) => void;
  onComplete?: () => void;
};

export function countUp({
  start = 0,
  end,
  duration = 1000,
  onUpdate,
  onComplete,
}: CountUpOptions) {
  const startTime = performance.now();
  const range = end - start;

  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const value = Math.floor(start + range * easeOutQuad(progress));

    if (onUpdate) onUpdate(value);

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else if (onComplete) {
      onComplete();
    }
  };

  requestAnimationFrame(animate);
}

// Easing function (can replace with easeInOutCubic, etc.)
function easeOutQuad(t: number): number {
  return t * (2 - t);
}
