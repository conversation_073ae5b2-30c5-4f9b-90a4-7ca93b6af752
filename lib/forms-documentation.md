# Forms Library Documentation

A comprehensive, single-file forms library for React applications with TypeScript, Tailwind CSS, and accessibility built-in.

## 📦 Import

All form components are available from a single import:

```tsx
import {
  // Core Components
  Input,
  EmailInput,
  PasswordInput,
  NumberInput,
  Select,
  CategorySelect,
  CurrencySelect,
  Textarea,
  Checkbox,
  Button,
  SubmitButton,
  CancelButton,
  DeleteButton,
  SaveButton,
  
  // Specialized Components
  CurrencyInput,
  SearchInput,
  
  // Layout Components
  FormGroup,
  FormSection,
  FormGrid,
  FormCard,
  FormActions,
  FormDivider,
} from "@/lib/forms";
```

## 🚀 Quick Examples

### Basic Form
```tsx
import { FormCard, Input, EmailInput, SubmitButton } from "@/lib/forms";

function ContactForm() {
  return (
    <FormCard title="Contact Us">
      <form className="space-y-4">
        <Input name="name" label="Name" required />
        <EmailInput name="email" label="Email" required />
        <SubmitButton>Submit</SubmitButton>
      </form>
    </FormCard>
  );
}
```

### Product Form
```tsx
import {
  FormCard,
  FormGrid,
  Input,
  CategorySelect,
  CurrencyInput,
  Textarea,
  Checkbox,
  SubmitButton,
} from "@/lib/forms";

function ProductForm() {
  return (
    <FormCard title="Add Product">
      <form className="space-y-6">
        <FormGrid cols={2}>
          <Input name="name" label="Product Name" required />
          <CategorySelect name="category" label="Category" required />
          <CurrencyInput name="price" label="Price" required />
          <Input name="sku" label="SKU" required />
        </FormGrid>
        
        <Textarea name="description" label="Description" rows={3} />
        
        <Checkbox name="isActive" label="Active Product" />
        
        <SubmitButton>Save Product</SubmitButton>
      </form>
    </FormCard>
  );
}
```

### Search Form
```tsx
import { SearchInput, Select, Button, FormGrid } from "@/lib/forms";

function ProductSearch() {
  const categoryOptions = [
    { value: "", label: "All Categories" },
    { value: "electronics", label: "Electronics" },
    { value: "clothing", label: "Clothing" },
  ];

  return (
    <FormGrid cols={3} gap="md">
      <SearchInput
        name="search"
        placeholder="Search products..."
        onSearch={(query) => console.log(query)}
      />
      
      <Select
        name="category"
        options={categoryOptions}
        placeholder="Category"
      />
      
      <Button variant="primary">Search</Button>
    </FormGrid>
  );
}
```

## 🎨 Component Features

### Input Components
- **Input**: Basic text input with variants
- **EmailInput**: Email input with validation
- **PasswordInput**: Password input with security
- **NumberInput**: Numeric input with constraints
- **SearchInput**: Search with debouncing and clear button
- **CurrencyInput**: Formatted currency input

### Selection Components
- **Select**: Dropdown with options
- **CategorySelect**: Pre-built product categories
- **CurrencySelect**: Currency selection
- **Checkbox**: Single checkbox with label

### Layout Components
- **FormCard**: Card wrapper for forms
- **FormGrid**: Responsive grid layout
- **FormSection**: Section with title/description
- **FormActions**: Button group alignment
- **FormDivider**: Visual separator

### Button Components
- **Button**: Base button with variants
- **SubmitButton**: Form submission
- **CancelButton**: Cancel action
- **DeleteButton**: Destructive action
- **SaveButton**: Save action

## 🔧 Advanced Features

### Validation Support
```tsx
<Input
  name="email"
  label="Email"
  error="Please enter a valid email"
  helpText="We'll never share your email"
/>
```

### Loading States
```tsx
<SubmitButton loading={isSubmitting}>
  {isSubmitting ? "Saving..." : "Save"}
</SubmitButton>
```

### Currency Formatting
```tsx
<CurrencyInput
  name="price"
  label="Price"
  currency="USD"
  locale="en-US"
  minValue={0}
  value={price}
  onChange={setPrice}
/>
```

### Responsive Grids
```tsx
<FormGrid cols={1} className="sm:grid-cols-2 lg:grid-cols-3">
  {/* 1 col mobile, 2 tablet, 3 desktop */}
</FormGrid>
```

### Search with Debouncing
```tsx
<SearchInput
  name="search"
  onSearch={handleSearch}
  debounceMs={300}
  showClearButton
/>
```

## 🎯 Best Practices

1. **Always provide labels** for accessibility
2. **Use appropriate input types** (email, password, etc.)
3. **Include error handling** for better UX
4. **Add help text** for complex fields
5. **Use FormGrid** for responsive layouts
6. **Group related fields** with FormSection
7. **Provide loading states** for async operations

## 🌙 Dark Mode

All components automatically support dark mode through Tailwind's `dark:` classes. No additional configuration needed!

## ♿ Accessibility

- ARIA labels and roles
- Keyboard navigation
- Screen reader support
- Focus management
- Error announcements

## 🔗 Integration

Works seamlessly with:
- **React Hook Form** - Form state management
- **Zod** - Schema validation
- **Next.js** - Server-side rendering
- **Tailwind CSS** - Styling system
- **TypeScript** - Type safety

## 📱 Mobile Support

All components are mobile-first and responsive:
- Touch-friendly sizing
- Responsive layouts
- Mobile-optimized interactions
- Proper viewport handling

## 🎨 Customization

### Custom Styling
```tsx
<Input
  name="custom"
  className="border-purple-300 focus:border-purple-500"
/>
```

### Button Variants
```tsx
<Button variant="primary">Primary</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="danger">Danger</Button>
<Button variant="success">Success</Button>
<Button variant="warning">Warning</Button>
<Button variant="ghost">Ghost</Button>
```

### Form Layout
```tsx
<FormSection title="Personal Info" description="Your details">
  <FormGrid cols={2} gap="lg">
    <Input name="firstName" label="First Name" />
    <Input name="lastName" label="Last Name" />
  </FormGrid>
</FormSection>
```

This single-file forms library provides everything you need for building consistent, accessible, and beautiful forms in your React applications!
