import { z } from "zod";

// ============================================================================
// BASE SCHEMAS - Core validation schemas matching database models
// ============================================================================

// Enum schemas
export const RoleSchema = z.enum(["ADMIN", "MANAGER", "CASHIER"]);
export const PaymentTypeSchema = z.enum(["CASH", "CARD", "MOBILE"]);
export const SaleStatusSchema = z.enum(["PENDING", "COMPLETED", "CANCELLED", "REFUNDED"]);
export const AdjustmentTypeSchema = z.enum(["ADD", "REMOVE", "SET"]);

// Base entity schemas
export const UserSchema = z.object({
  id: z.string().cuid(),
  email: z.string().email("Invalid email address"),
  name: z.string().nullable(),
  role: RoleSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CategorySchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Category name is required").max(100, "Category name too long"),
  description: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const ProductSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Product name is required").max(200, "Product name too long"),
  description: z.string().nullable(),
  barcode: z.string().nullable(),
  sku: z.string().nullable(),
  price: z.number().positive("Price must be positive"),
  cost: z.number().positive("Cost must be positive").nullable(),
  stock: z.number().int().min(0, "Stock cannot be negative"),
  minStock: z.number().int().min(0, "Minimum stock cannot be negative"),
  categoryId: z.string().cuid(),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const SaleSchema = z.object({
  id: z.string().cuid(),
  total: z.number().positive("Total must be positive"),
  tax: z.number().min(0, "Tax cannot be negative").nullable(),
  discount: z.number().min(0, "Discount cannot be negative").nullable(),
  paymentType: PaymentTypeSchema,
  status: SaleStatusSchema,
  userId: z.string().cuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const SaleItemSchema = z.object({
  id: z.string().cuid(),
  quantity: z.number().int().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive"),
  total: z.number().positive("Total must be positive"),
  saleId: z.string().cuid(),
  productId: z.string().cuid(),
});

export const StockAdjustmentSchema = z.object({
  id: z.string().cuid(),
  quantity: z.number().int(),
  type: AdjustmentTypeSchema,
  reason: z.string().nullable(),
  productId: z.string().cuid(),
  userId: z.string().cuid(),
  createdAt: z.date(),
});

// ============================================================================
// INPUT DTOs - For creating and updating entities
// ============================================================================

// User DTOs
export const CreateUserSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  role: RoleSchema.default("CASHIER"),
});

export const UpdateUserSchema = CreateUserSchema.partial();

// Category DTOs
export const CreateCategorySchema = z.object({
  name: z.string().min(1, "Category name is required").max(100, "Category name too long"),
  description: z.string().max(500, "Description too long").optional(),
});

export const UpdateCategorySchema = CreateCategorySchema.partial();

// Product DTOs
export const CreateProductSchema = z.object({
  name: z.string().min(1, "Product name is required").max(200, "Product name too long"),
  description: z.string().max(1000, "Description too long").optional(),
  barcode: z.string().max(50, "Barcode too long").optional(),
  sku: z.string().max(50, "SKU too long").optional(),
  price: z.number().positive("Price must be positive"),
  cost: z.number().positive("Cost must be positive").optional(),
  stock: z.number().int().min(0, "Stock cannot be negative").default(0),
  minStock: z.number().int().min(0, "Minimum stock cannot be negative").default(0),
  categoryId: z.string().cuid("Invalid category"),
  isActive: z.boolean().default(true),
});

export const UpdateProductSchema = CreateProductSchema.partial();

// Sale DTOs
export const CreateSaleItemSchema = z.object({
  productId: z.string().cuid("Invalid product"),
  quantity: z.number().int().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive"),
});

export const CreateSaleSchema = z.object({
  items: z.array(CreateSaleItemSchema).min(1, "At least one item is required"),
  tax: z.number().min(0, "Tax cannot be negative").optional(),
  discount: z.number().min(0, "Discount cannot be negative").optional(),
  paymentType: PaymentTypeSchema,
  userId: z.string().cuid("Invalid user"),
});

// Stock Adjustment DTOs
export const CreateStockAdjustmentSchema = z.object({
  productId: z.string().cuid("Invalid product"),
  quantity: z.number().int(),
  type: AdjustmentTypeSchema,
  reason: z.string().max(500, "Reason too long").optional(),
  userId: z.string().cuid("Invalid user"),
});

// ============================================================================
// FORM DTOs - Specific to form inputs with string handling
// ============================================================================

// Product Form DTO (handles string inputs from forms)
export const ProductFormSchema = z.object({
  name: z.string().min(1, "Product name is required").max(200, "Product name too long"),
  description: z.string().max(1000, "Description too long").optional(),
  barcode: z.string().max(50, "Barcode too long").optional(),
  sku: z.string().max(50, "SKU too long").optional(),
  price: z.string().min(1, "Price is required").transform((val) => {
    const num = parseFloat(val);
    if (isNaN(num) || num <= 0) throw new Error("Price must be a positive number");
    return num;
  }),
  cost: z.string().optional().transform((val) => {
    if (!val || val === "") return undefined;
    const num = parseFloat(val);
    if (isNaN(num) || num <= 0) throw new Error("Cost must be a positive number");
    return num;
  }),
  stock: z.string().transform((val) => {
    const num = parseInt(val);
    if (isNaN(num) || num < 0) throw new Error("Stock must be a non-negative number");
    return num;
  }),
  minStock: z.string().transform((val) => {
    const num = parseInt(val);
    if (isNaN(num) || num < 0) throw new Error("Minimum stock must be a non-negative number");
    return num;
  }),
  categoryId: z.string().cuid("Please select a category"),
  isActive: z.boolean().default(true),
});

// Category Form DTO
export const CategoryFormSchema = z.object({
  name: z.string().min(1, "Category name is required").max(100, "Category name too long"),
  description: z.string().max(500, "Description too long").optional(),
});

// User Form DTO
export const UserFormSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  role: RoleSchema,
});

// Sale Form DTO
export const SaleFormSchema = z.object({
  items: z.array(z.object({
    productId: z.string().cuid("Invalid product"),
    quantity: z.string().transform((val) => {
      const num = parseInt(val);
      if (isNaN(num) || num <= 0) throw new Error("Quantity must be a positive number");
      return num;
    }),
    price: z.string().transform((val) => {
      const num = parseFloat(val);
      if (isNaN(num) || num <= 0) throw new Error("Price must be a positive number");
      return num;
    }),
  })).min(1, "At least one item is required"),
  tax: z.string().optional().transform((val) => {
    if (!val || val === "") return undefined;
    const num = parseFloat(val);
    if (isNaN(num) || num < 0) throw new Error("Tax must be a non-negative number");
    return num;
  }),
  discount: z.string().optional().transform((val) => {
    if (!val || val === "") return undefined;
    const num = parseFloat(val);
    if (isNaN(num) || num < 0) throw new Error("Discount must be a non-negative number");
    return num;
  }),
  paymentType: PaymentTypeSchema,
  userId: z.string().cuid("Invalid user"),
});

// ============================================================================
// TYPE EXPORTS - TypeScript types derived from schemas
// ============================================================================

// Base types
export type User = z.infer<typeof UserSchema>;
export type Category = z.infer<typeof CategorySchema>;
export type Product = z.infer<typeof ProductSchema>;
export type Sale = z.infer<typeof SaleSchema>;
export type SaleItem = z.infer<typeof SaleItemSchema>;
export type StockAdjustment = z.infer<typeof StockAdjustmentSchema>;

// Input types
export type CreateUser = z.infer<typeof CreateUserSchema>;
export type UpdateUser = z.infer<typeof UpdateUserSchema>;
export type CreateCategory = z.infer<typeof CreateCategorySchema>;
export type UpdateCategory = z.infer<typeof UpdateCategorySchema>;
export type CreateProduct = z.infer<typeof CreateProductSchema>;
export type UpdateProduct = z.infer<typeof UpdateProductSchema>;
export type CreateSale = z.infer<typeof CreateSaleSchema>;
export type CreateSaleItem = z.infer<typeof CreateSaleItemSchema>;
export type CreateStockAdjustment = z.infer<typeof CreateStockAdjustmentSchema>;

// Form types
export type ProductForm = z.infer<typeof ProductFormSchema>;
export type CategoryForm = z.infer<typeof CategoryFormSchema>;
export type UserForm = z.infer<typeof UserFormSchema>;
export type SaleForm = z.infer<typeof SaleFormSchema>;

// Enum types
export type Role = z.infer<typeof RoleSchema>;
export type PaymentType = z.infer<typeof PaymentTypeSchema>;
export type SaleStatus = z.infer<typeof SaleStatusSchema>;
export type AdjustmentType = z.infer<typeof AdjustmentTypeSchema>;
