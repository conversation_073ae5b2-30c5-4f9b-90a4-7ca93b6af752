import { z } from "zod";

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validation result type
 */
export type ValidationResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  errors: Record<string, string>;
  fieldErrors: Record<string, string[]>;
};

/**
 * Validates data against a Zod schema and returns formatted errors
 */
export function validateSchema<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): ValidationResult<T> {
  try {
    const result = schema.parse(data);
    return {
      success: true,
      data: result,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      const fieldErrors: Record<string, string[]> = {};

      error.errors.forEach((err) => {
        const path = err.path.join(".");
        const message = err.message;

        // Set the first error message for each field
        if (!errors[path]) {
          errors[path] = message;
        }

        // Collect all error messages for each field
        if (!fieldErrors[path]) {
          fieldErrors[path] = [];
        }
        fieldErrors[path].push(message);
      });

      return {
        success: false,
        errors,
        fieldErrors,
      };
    }

    // Handle unexpected errors
    return {
      success: false,
      errors: { _form: "An unexpected error occurred" },
      fieldErrors: { _form: ["An unexpected error occurred"] },
    };
  }
}

/**
 * Validates form data and returns errors in a format suitable for form libraries
 */
export function validateFormData<T>(
  schema: z.ZodSchema<T>,
  formData: FormData | Record<string, any>
): ValidationResult<T> {
  let data: Record<string, any>;

  if (formData instanceof FormData) {
    data = Object.fromEntries(formData.entries());
  } else {
    data = formData;
  }

  return validateSchema(schema, data);
}

/**
 * Async validation function for use with react-hook-form
 */
export function createAsyncValidator<T>(schema: z.ZodSchema<T>) {
  return async (data: unknown): Promise<T> => {
    const result = validateSchema(schema, data);
    if (!result.success) {
      throw new Error(JSON.stringify(result.errors));
    }
    return result.data;
  };
}

/**
 * Creates a validation function that returns boolean for simple validation
 */
export function createValidator<T>(schema: z.ZodSchema<T>) {
  return (data: unknown): data is T => {
    try {
      schema.parse(data);
      return true;
    } catch {
      return false;
    }
  };
}

// ============================================================================
// FORM INTEGRATION HELPERS
// ============================================================================

/**
 * Hook for form validation with real-time error updates
 */
export function useFormValidation<T>(schema: z.ZodSchema<T>) {
  const validate = (data: unknown) => validateSchema(schema, data);
  
  const validateField = (fieldName: string, value: unknown) => {
    try {
      // Create a partial schema for single field validation
      const fieldSchema = schema.shape?.[fieldName as keyof typeof schema.shape];
      if (fieldSchema) {
        fieldSchema.parse(value);
        return null; // No error
      }
      return null;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message || "Invalid value";
      }
      return "Invalid value";
    }
  };

  return {
    validate,
    validateField,
  };
}

/**
 * Transforms form data to match expected types (e.g., string to number)
 */
export function transformFormData(data: Record<string, any>): Record<string, any> {
  const transformed: Record<string, any> = {};

  for (const [key, value] of Object.entries(data)) {
    if (value === "" || value === null || value === undefined) {
      transformed[key] = undefined;
    } else if (typeof value === "string") {
      // Try to convert string numbers
      const num = Number(value);
      if (!isNaN(num) && value.trim() !== "") {
        transformed[key] = num;
      } else if (value.toLowerCase() === "true") {
        transformed[key] = true;
      } else if (value.toLowerCase() === "false") {
        transformed[key] = false;
      } else {
        transformed[key] = value;
      }
    } else {
      transformed[key] = value;
    }
  }

  return transformed;
}

// ============================================================================
// API VALIDATION MIDDLEWARE
// ============================================================================

/**
 * Creates a validation middleware for API routes
 */
export function createApiValidator<T>(schema: z.ZodSchema<T>) {
  return (data: unknown) => {
    const result = validateSchema(schema, data);
    if (!result.success) {
      const error = new Error("Validation failed");
      (error as any).statusCode = 400;
      (error as any).errors = result.errors;
      (error as any).fieldErrors = result.fieldErrors;
      throw error;
    }
    return result.data;
  };
}

/**
 * Validates request body for API routes
 */
export async function validateRequestBody<T>(
  request: Request,
  schema: z.ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json();
    return createApiValidator(schema)(body);
  } catch (error) {
    if ((error as any).statusCode === 400) {
      throw error;
    }
    const validationError = new Error("Invalid request body");
    (validationError as any).statusCode = 400;
    throw validationError;
  }
}

// ============================================================================
// COMMON VALIDATION PATTERNS
// ============================================================================

/**
 * Common validation schemas for reuse
 */
export const CommonValidations = {
  // ID validation
  id: z.string().cuid("Invalid ID format"),
  
  // Email validation
  email: z.string().email("Invalid email address"),
  
  // Password validation
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number"),
  
  // Phone validation
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, "Invalid phone number format"),
  
  // Currency validation
  currency: z.number().positive("Amount must be positive").multipleOf(0.01, "Invalid currency format"),
  
  // Percentage validation
  percentage: z.number().min(0, "Percentage cannot be negative").max(100, "Percentage cannot exceed 100"),
  
  // Date validation
  futureDate: z.date().refine((date) => date > new Date(), "Date must be in the future"),
  pastDate: z.date().refine((date) => date < new Date(), "Date must be in the past"),
  
  // File validation
  imageFile: z.object({
    name: z.string(),
    size: z.number().max(5 * 1024 * 1024, "File size must be less than 5MB"),
    type: z.string().regex(/^image\/(jpeg|jpg|png|gif|webp)$/, "File must be an image"),
  }),
};

/**
 * Utility to create conditional validation
 */
export function conditionalValidation<T>(
  condition: (data: any) => boolean,
  schema: z.ZodSchema<T>,
  fallbackSchema?: z.ZodSchema<any>
) {
  return z.any().superRefine((data, ctx) => {
    if (condition(data)) {
      const result = schema.safeParse(data);
      if (!result.success) {
        result.error.errors.forEach((error) => {
          ctx.addIssue(error);
        });
      }
    } else if (fallbackSchema) {
      const result = fallbackSchema.safeParse(data);
      if (!result.success) {
        result.error.errors.forEach((error) => {
          ctx.addIssue(error);
        });
      }
    }
  });
}
