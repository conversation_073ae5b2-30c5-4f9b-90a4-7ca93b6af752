import { useCallback, useState } from "react";
import { z } from "zod";
import { validateSchema, ValidationResult } from "./validation";

// ============================================================================
// FORM VALIDATION HOOKS
// ============================================================================

/**
 * Enhanced form validation hook with real-time validation
 */
export function useFormValidation<T>(schema: z.ZodSchema<T>) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);

  const validate = useCallback((data: unknown): ValidationResult<T> => {
    setIsValidating(true);
    const result = validateSchema(schema, data);
    
    if (result.success) {
      setErrors({});
    } else {
      setErrors(result.errors);
    }
    
    setIsValidating(false);
    return result;
  }, [schema]);

  const validateField = useCallback((fieldName: string, value: unknown) => {
    try {
      // For nested field validation, we need to validate the entire object
      // but only show errors for the specific field
      const currentErrors = { ...errors };
      
      // Clear the error for this field first
      delete currentErrors[fieldName];
      
      // Try to validate just this field if possible
      const fieldSchema = (schema as any).shape?.[fieldName];
      if (fieldSchema) {
        fieldSchema.parse(value);
        setErrors(currentErrors);
        return null;
      }
      
      return null;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldError = error.errors[0]?.message || "Invalid value";
        setErrors({
          ...errors,
          [fieldName]: fieldError,
        });
        return fieldError;
      }
      return "Invalid value";
    }
  }, [schema, errors]);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const clearFieldError = useCallback((fieldName: string) => {
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  return {
    errors,
    isValidating,
    validate,
    validateField,
    clearErrors,
    clearFieldError,
  };
}

/**
 * Form state management hook with validation
 */
export function useValidatedForm<T>(
  schema: z.ZodSchema<T>,
  initialData: Partial<T> = {}
) {
  const [formData, setFormData] = useState<Partial<T>>(initialData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { errors, validate, validateField, clearErrors, clearFieldError } = useFormValidation(schema);

  const updateField = useCallback((fieldName: keyof T, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
    
    // Clear error when user starts typing
    clearFieldError(fieldName as string);
  }, [clearFieldError]);

  const updateFields = useCallback((updates: Partial<T>) => {
    setFormData((prev) => ({
      ...prev,
      ...updates,
    }));
  }, []);

  const validateForm = useCallback(() => {
    return validate(formData);
  }, [validate, formData]);

  const submitForm = useCallback(async (
    onSubmit: (data: T) => Promise<void> | void
  ) => {
    setIsSubmitting(true);
    
    try {
      const result = validateForm();
      if (result.success) {
        await onSubmit(result.data);
        return { success: true, data: result.data };
      } else {
        return { success: false, errors: result.errors };
      }
    } catch (error) {
      console.error("Form submission error:", error);
      return { 
        success: false, 
        errors: { _form: "An error occurred while submitting the form" } 
      };
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm]);

  const resetForm = useCallback(() => {
    setFormData(initialData);
    clearErrors();
    setIsSubmitting(false);
  }, [initialData, clearErrors]);

  return {
    formData,
    errors,
    isSubmitting,
    updateField,
    updateFields,
    validateForm,
    validateField,
    submitForm,
    resetForm,
    clearErrors,
  };
}

// ============================================================================
// FORM FIELD HELPERS
// ============================================================================

/**
 * Creates props for form inputs with validation
 */
export function createFieldProps<T>(
  fieldName: keyof T,
  formData: Partial<T>,
  errors: Record<string, string>,
  updateField: (field: keyof T, value: any) => void,
  validateField?: (field: string, value: any) => void
) {
  return {
    name: fieldName as string,
    value: formData[fieldName] || "",
    error: errors[fieldName as string],
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
      const value = e.target.type === "checkbox" 
        ? (e.target as HTMLInputElement).checked 
        : e.target.value;
      updateField(fieldName, value);
    },
    onBlur: validateField ? () => {
      validateField(fieldName as string, formData[fieldName]);
    } : undefined,
  };
}

/**
 * Creates props for number inputs with proper type conversion
 */
export function createNumberFieldProps<T>(
  fieldName: keyof T,
  formData: Partial<T>,
  errors: Record<string, string>,
  updateField: (field: keyof T, value: any) => void,
  validateField?: (field: string, value: any) => void
) {
  return {
    name: fieldName as string,
    value: formData[fieldName]?.toString() || "",
    error: errors[fieldName as string],
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      updateField(fieldName, value);
    },
    onBlur: validateField ? () => {
      validateField(fieldName as string, formData[fieldName]);
    } : undefined,
  };
}

/**
 * Creates props for select inputs
 */
export function createSelectFieldProps<T>(
  fieldName: keyof T,
  formData: Partial<T>,
  errors: Record<string, string>,
  updateField: (field: keyof T, value: any) => void,
  validateField?: (field: string, value: any) => void
) {
  return {
    name: fieldName as string,
    value: formData[fieldName] || "",
    error: errors[fieldName as string],
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => {
      updateField(fieldName, e.target.value);
    },
    onBlur: validateField ? () => {
      validateField(fieldName as string, formData[fieldName]);
    } : undefined,
  };
}

// ============================================================================
// VALIDATION DECORATORS
// ============================================================================

/**
 * Higher-order component that adds validation to form components
 */
export function withValidation<P extends object>(
  Component: React.ComponentType<P>,
  schema: z.ZodSchema<any>
) {
  return function ValidatedComponent(props: P & { onValidate?: (isValid: boolean) => void }) {
    const { onValidate, ...componentProps } = props;
    const { validate } = useFormValidation(schema);

    const handleValidation = useCallback((data: any) => {
      const result = validate(data);
      onValidate?.(result.success);
      return result;
    }, [validate, onValidate]);

    return <Component {...(componentProps as P)} onValidate={handleValidation} />;
  };
}

// ============================================================================
// FORM UTILITIES
// ============================================================================

/**
 * Converts form data to the correct types for validation
 */
export function prepareFormData(formData: FormData): Record<string, any> {
  const data: Record<string, any> = {};
  
  for (const [key, value] of formData.entries()) {
    if (typeof value === "string") {
      // Handle checkboxes
      if (value === "on") {
        data[key] = true;
      } else if (value === "off" || value === "") {
        data[key] = false;
      } else {
        data[key] = value;
      }
    } else {
      data[key] = value;
    }
  }
  
  return data;
}

/**
 * Creates a form submission handler with validation
 */
export function createFormSubmissionHandler<T>(
  schema: z.ZodSchema<T>,
  onSubmit: (data: T) => Promise<void> | void,
  onError?: (errors: Record<string, string>) => void
) {
  return async (formData: FormData | Record<string, any>) => {
    try {
      const data = formData instanceof FormData 
        ? prepareFormData(formData) 
        : formData;
      
      const result = validateSchema(schema, data);
      
      if (result.success) {
        await onSubmit(result.data);
        return { success: true, data: result.data };
      } else {
        onError?.(result.errors);
        return { success: false, errors: result.errors };
      }
    } catch (error) {
      console.error("Form submission error:", error);
      const errorMessage = "An error occurred while submitting the form";
      onError?.({ _form: errorMessage });
      return { success: false, errors: { _form: errorMessage } };
    }
  };
}
