"use client";

import { useState } from "react";
import {
  FormCard,
  FormSection,
  FormGrid,
  FormActions,
  FormDivider,
  Input,
  EmailInput,
  PasswordInput,
  NumberInput,
  Textarea,
  Select,
  CategorySelect,
  CurrencySelect,
  Checkbox,
  CurrencyInput,
  SearchInput,
  Button,
  SubmitButton,
  CancelButton,
  DeleteButton,
  SaveButton,
} from "./forms";

// ============================================================================
// EXAMPLE 1: CONTACT FORM
// ============================================================================

export function ContactFormExample() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const subjectOptions = [
    { value: "general", label: "General Inquiry" },
    { value: "support", label: "Technical Support" },
    { value: "billing", label: "Billing Question" },
    { value: "feedback", label: "Feedback" },
  ];

  return (
    <FormCard title="Contact Us" description="Send us a message and we'll get back to you">
      <form className="space-y-6">
        <FormGrid cols={2} gap="md">
          <Input
            name="name"
            label="Full Name"
            placeholder="Enter your name"
            required
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          />

          <EmailInput
            name="email"
            label="Email Address"
            placeholder="Enter your email"
            required
            value={formData.email}
            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
          />
        </FormGrid>

        <Select
          name="subject"
          label="Subject"
          options={subjectOptions}
          placeholder="Select a subject"
          required
          value={formData.subject}
          onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
        />

        <Textarea
          name="message"
          label="Message"
          placeholder="Enter your message"
          rows={4}
          required
          value={formData.message}
          onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
        />

        <FormActions align="right">
          <CancelButton>Cancel</CancelButton>
          <SubmitButton>Send Message</SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}

// ============================================================================
// EXAMPLE 2: PRODUCT FORM
// ============================================================================

export function ProductFormExample() {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "",
    price: 0,
    costPrice: 0,
    stock: 0,
    sku: "",
    isActive: true,
    isFeatured: false,
  });

  const supplierOptions = [
    { value: "supplier1", label: "Local Organic Farms" },
    { value: "supplier2", label: "Fresh Foods Inc." },
    { value: "supplier3", label: "Global Distributors" },
  ];

  return (
    <form className="space-y-8">
      <FormSection
        title="Product Information"
        description="Basic product details and description"
      >
        <FormGrid cols={2} gap="md">
          <Input
            name="name"
            label="Product Name"
            placeholder="Enter product name"
            required
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          />

          <Input
            name="sku"
            label="SKU"
            placeholder="Product SKU"
            required
            value={formData.sku}
            onChange={(e) => setFormData(prev => ({ ...prev, sku: e.target.value }))}
          />

          <CategorySelect
            name="category"
            label="Category"
            required
            value={formData.category}
            onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
          />

          <Select
            name="supplier"
            label="Supplier"
            options={supplierOptions}
            placeholder="Select supplier"
          />
        </FormGrid>

        <Textarea
          name="description"
          label="Description"
          placeholder="Product description"
          rows={3}
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          helpText="Provide a detailed description of the product"
        />
      </FormSection>

      <FormSection
        title="Pricing & Inventory"
        description="Set pricing and stock information"
      >
        <FormGrid cols={3} gap="md">
          <CurrencyInput
            name="costPrice"
            label="Cost Price"
            required
            value={formData.costPrice}
            onChange={(value) => setFormData(prev => ({ ...prev, costPrice: value || 0 }))}
            helpText="Your cost for this product"
          />

          <CurrencyInput
            name="price"
            label="Selling Price"
            required
            value={formData.price}
            onChange={(value) => setFormData(prev => ({ ...prev, price: value || 0 }))}
            helpText="Customer price"
          />

          <NumberInput
            name="stock"
            label="Stock Quantity"
            required
            min={0}
            value={formData.stock.toString()}
            onChange={(e) => setFormData(prev => ({ ...prev, stock: parseInt(e.target.value) || 0 }))}
          />
        </FormGrid>

        <FormGrid cols={2} gap="md">
          <Checkbox
            name="isActive"
            label="Active Product"
            checked={formData.isActive}
            onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
            helpText="Product is available for sale"
          />

          <Checkbox
            name="isFeatured"
            label="Featured Product"
            checked={formData.isFeatured}
            onChange={(e) => setFormData(prev => ({ ...prev, isFeatured: e.target.checked }))}
            helpText="Show in featured products section"
          />
        </FormGrid>
      </FormSection>

      <FormActions align="between">
        <DeleteButton>Delete Product</DeleteButton>
        <div className="flex space-x-3">
          <CancelButton>Cancel</CancelButton>
          <SaveButton>Save Product</SaveButton>
        </div>
      </FormActions>
    </form>
  );
}

// ============================================================================
// EXAMPLE 3: SEARCH FORM
// ============================================================================

export function SearchFormExample() {
  const [filters, setFilters] = useState({
    query: "",
    category: "",
    priceRange: "",
    inStock: false,
  });

  const categoryOptions = [
    { value: "", label: "All Categories" },
    { value: "electronics", label: "Electronics" },
    { value: "clothing", label: "Clothing" },
    { value: "books", label: "Books" },
    { value: "home", label: "Home & Garden" },
  ];

  const priceRangeOptions = [
    { value: "", label: "Any Price" },
    { value: "0-25", label: "$0 - $25" },
    { value: "25-50", label: "$25 - $50" },
    { value: "50-100", label: "$50 - $100" },
    { value: "100+", label: "$100+" },
  ];

  return (
    <FormCard title="Product Search" description="Find products using filters">
      <div className="space-y-6">
        <SearchInput
          name="search"
          label="Search Products"
          placeholder="Search by name, description, or SKU..."
          value={filters.query}
          onSearch={(query) => setFilters(prev => ({ ...prev, query }))}
          debounceMs={300}
          showClearButton
        />

        <FormGrid cols={3} gap="md">
          <Select
            name="category"
            label="Category"
            options={categoryOptions}
            value={filters.category}
            onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
          />

          <Select
            name="priceRange"
            label="Price Range"
            options={priceRangeOptions}
            value={filters.priceRange}
            onChange={(e) => setFilters(prev => ({ ...prev, priceRange: e.target.value }))}
          />

          <div className="flex items-end">
            <Checkbox
              name="inStock"
              label="In Stock Only"
              checked={filters.inStock}
              onChange={(e) => setFilters(prev => ({ ...prev, inStock: e.target.checked }))}
            />
          </div>
        </FormGrid>

        <FormDivider />

        <FormActions align="between">
          <Button variant="ghost" onClick={() => setFilters({ query: "", category: "", priceRange: "", inStock: false })}>
            Clear Filters
          </Button>
          <Button variant="primary">Search Products</Button>
        </FormActions>
      </div>
    </FormCard>
  );
}

// ============================================================================
// EXAMPLE 4: USER REGISTRATION
// ============================================================================

export function RegistrationFormExample() {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: false,
    newsletter: false,
  });

  return (
    <FormCard title="Create Account" description="Join us today">
      <form className="space-y-6">
        <FormGrid cols={2} gap="md">
          <Input
            name="firstName"
            label="First Name"
            placeholder="Enter your first name"
            required
            value={formData.firstName}
            onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
          />

          <Input
            name="lastName"
            label="Last Name"
            placeholder="Enter your last name"
            required
            value={formData.lastName}
            onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
          />
        </FormGrid>

        <EmailInput
          name="email"
          label="Email Address"
          placeholder="Enter your email"
          required
          value={formData.email}
          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
        />

        <FormGrid cols={2} gap="md">
          <PasswordInput
            name="password"
            label="Password"
            placeholder="Create a password"
            required
            value={formData.password}
            onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
            helpText="Must be at least 8 characters"
          />

          <PasswordInput
            name="confirmPassword"
            label="Confirm Password"
            placeholder="Confirm your password"
            required
            value={formData.confirmPassword}
            onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
          />
        </FormGrid>

        <div className="space-y-3">
          <Checkbox
            name="agreeToTerms"
            label="I agree to the Terms of Service and Privacy Policy"
            required
            checked={formData.agreeToTerms}
            onChange={(e) => setFormData(prev => ({ ...prev, agreeToTerms: e.target.checked }))}
          />

          <Checkbox
            name="newsletter"
            label="Subscribe to our newsletter for updates and offers"
            checked={formData.newsletter}
            onChange={(e) => setFormData(prev => ({ ...prev, newsletter: e.target.checked }))}
          />
        </div>

        <FormActions>
          <SubmitButton className="w-full">Create Account</SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}
