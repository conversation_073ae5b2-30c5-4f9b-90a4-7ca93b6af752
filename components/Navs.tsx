"use client";

import { NavLink } from "@/types";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

import {
  HiMenu,
  HiOutlineCog,
  HiOutlineShoppingCart,
  HiOutlineUsers,
  HiOutlineViewGrid,
} from "react-icons/hi";

export const defaultAdminLinks: NavLink[] = [
  {
    name: "Dashboard",
    href: "/",
    icon: <HiOutlineViewGrid className="h-5 w-5" />,
  },
  {
    name: "Users",
    href: "/users",
    icon: <HiOutlineUsers className="h-5 w-5" />,
  },
  {
    name: "Orders",
    href: "/orders",
    icon: <HiOutlineShoppingCart className="h-5 w-5" />,
  },
  {
    name: "Settings",
    href: "/settings",
    icon: <HiOutlineCog className="h-5 w-5" />,
  },
];

export const SidebarNav = ({
  links = defaultAdminLinks,
  isCollapsed = false,
}: {
  links?: NavLink[];
  isCollapsed?: boolean;
}) => {
  const pathname = usePathname();

  const isActive = (href: string) =>
    pathname === href || pathname.startsWith(href);

  return (
    <aside
      className={`fixed top-0 left-0 z-40 h-screen w-64 border-r border-gray-200 bg-white transition-transform dark:border-gray-700 dark:bg-gray-800 ${
        isCollapsed ? "-translate-x-full" : "translate-x-0"
      }`}
      aria-label="Sidebar"
    >
      <div className="h-full overflow-y-auto px-3 py-4">
        <ul className="space-y-2 font-medium">
          {links.map((link) => (
            <li key={link.href}>
              <Link
                href={link.href}
                className={`group flex items-center rounded-lg p-2 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700 ${
                  isActive(link.href) ? "bg-gray-100 dark:bg-gray-700" : ""
                }`}
              >
                {link.icon}
                <span className="ml-3">{link.name}</span>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </aside>
  );
};

export const TopNavbar = ({
  onToggleSidebar,
  title = "Admin Dashboard",
}: {
  onToggleSidebar: () => void;
  title?: string;
}) => {
  return (
    <nav className="fixed top-0 left-0 z-50 w-full border-b border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
      <div className="flex items-center justify-between px-3 py-3 lg:px-5 lg:pl-3">
        <button
          onClick={onToggleSidebar}
          className="inline-flex items-center rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none lg:hidden dark:text-gray-400 dark:hover:bg-gray-700"
          aria-label="Toggle sidebar"
        >
          <HiMenu className="h-6 w-6" />
          <span className="sr-only">Open sidebar</span>
        </button>
        <div className="flex items-center">
          <span className="self-center text-xl font-semibold whitespace-nowrap dark:text-white">
            {title}
          </span>
        </div>
      </div>
    </nav>
  );
};

// Complete admin layout component
export const AdminLayout = ({
  children,
  title = "Admin Dashboard",
  sidebarLinks = defaultAdminLinks,
}: {
  children: React.ReactNode;
  title?: string;
  sidebarLinks?: NavLink[];
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <TopNavbar onToggleSidebar={toggleSidebar} title={title} />
      <SidebarNav links={sidebarLinks} isCollapsed={sidebarCollapsed} />

      {/* Main content area */}
      <div
        className={`transition-all duration-300 ${sidebarCollapsed ? "ml-0" : "ml-64"} pt-16`}
      >
        <main className="p-4">{children}</main>
      </div>

      {/* Mobile overlay */}
      {!sidebarCollapsed && (
        <div
          className="bg-opacity-50 fixed inset-0 z-30 bg-black lg:hidden"
          onClick={toggleSidebar}
          aria-hidden="true"
        />
      )}
    </div>
  );
};
