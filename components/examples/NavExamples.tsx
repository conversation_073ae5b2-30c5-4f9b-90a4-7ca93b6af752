// Example usage of the reusable HeadNav component

import { HeadNav, EcommerceNav, MinimalNav } from "../Navs";

// Example 1: Basic usage with default settings
export const BasicNavExample = () => {
  return <HeadNav />;
};

// Example 2: Custom navigation links
export const CustomLinksExample = () => {
  const customLinks = [
    { name: "Dashboard", href: "/dashboard" },
    { name: "Analytics", href: "/analytics" },
    { name: "Settings", href: "/settings" },
    { name: "Profile", href: "/profile" },
  ];

  return (
    <HeadNav
      links={customLinks}
      logo={{
        src: "/custom-logo.svg",
        alt: "My App",
        text: "MyApp",
      }}
    />
  );
};

// Example 3: Using the pre-built ecommerce nav
export const EcommerceExample = () => {
  return <EcommerceNav />;
};

// Example 4: Minimal nav without logo text
export const MinimalExample = () => {
  const simpleLinks = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
  ];

  return <MinimalNav links={simpleLinks} />;
};

// Example 5: Nav with additional styling
export const StyledNavExample = () => {
  return (
    <HeadNav
      className="border-b-2 border-blue-500"
      logo={{
        src: "/logo.svg",
        alt: "Styled App",
        text: "StyledApp",
      }}
    />
  );
};
