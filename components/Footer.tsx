import { logoutAccount } from "@/lib/user";
import { useRouter } from "next/navigation";

const Footer = ({ user }: FooterProps) => {
  const router = useRouter();

  const handleLogOut = async () => {
    const loggedOut = await logoutAccount();

    if (loggedOut) router.push("/sign-in");
  };

  return (
    <footer className="footer border-slate-100 bg-white dark:bg-slate-900 dark:text-slate-100">
      {/* <!-- Footer --> */}
      <div className="mx-auto w-full p-1 md:py-2 lg:p-2">
        <hr className="mb-1 border-slate-200 sm:mx-1 lg:mb-2 dark:border-slate-700" />

        <div className="sm:flex sm:items-center sm:justify-between">
          <span className="text-sm text-slate-500 sm:text-center dark:text-slate-400">
            &copy; &nbsp; {new Date().getFullYear()}{" "}
            <a href="#" className="hover:underline">
              eSTOR™
            </a>
            . All Rights Reserved.
          </span>
          <span
            className="text-sm text-slate-600 dark:text-slate-400"
            role="button"
          >
            Design and build by{" "}
            <a
              href="https://t.me/ntes0"
              target="_blank"
              className="mr-4 hover:underline md:mr-6"
            >
              <strong>Bromon Technologies</strong>
            </a>
          </span>

          <div className="mt-4 flex space-x-5 sm:mt-0 sm:justify-center">
            <ul className="mb-6 flex flex-wrap items-center text-sm font-medium text-slate-500 sm:mb-0 dark:text-slate-400">
              <li>
                <a
                  href="/about"
                  className="mr-4 block rounded py-2 pr-4 pl-3 text-slate-900 hover:bg-slate-100 hover:underline md:mr-6 md:border-0 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 dark:text-white dark:hover:bg-slate-700 dark:hover:text-white md:dark:hover:bg-transparent md:dark:hover:text-blue-500"
                >
                  About
                </a>
              </li>
              <li>
                <a
                  href="/policy"
                  className="mr-4 block rounded py-2 pr-4 pl-3 text-slate-900 hover:bg-slate-100 hover:underline md:mr-6 md:border-0 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 dark:text-white dark:hover:bg-slate-700 dark:hover:text-white md:dark:hover:bg-transparent md:dark:hover:text-blue-500"
                >
                  Privacy Policy
                </a>
              </li>
              <li>
                <a
                  href="/contact"
                  className="block rounded py-2 pr-4 pl-3 text-slate-900 hover:bg-slate-100 hover:underline md:border-0 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 dark:text-white dark:hover:bg-slate-700 dark:hover:text-white md:dark:hover:bg-transparent md:dark:hover:text-blue-500"
                >
                  Contact
                </a>
              </li>
            </ul>
            <a
              href="https://m.facebook.com/{{store_info.facebook"
              target="_blank"
              className="text-slate-500 hover:text-slate-900 dark:hover:text-white"
            >
              {/* <!-- Facebook --> */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="currentColor"
                viewBox="0 0 24 24"
                style={{ color: "#1877f2" }}
              >
                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
              </svg>
              <span className="sr-only">Facebook</span>
            </a>
            <a
              href="https://wa.me/{{store_info.whatsapp"
              target="_blank"
              className="text-slate-500 hover:text-slate-900 dark:hover:text-white"
            >
              {/* <!-- Whatsapp --> */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="currentColor"
                viewBox="0 0 24 24"
                style={{ color: "#128c7e" }}
              >
                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z" />
              </svg>
              <span className="sr-only">WhatsApp</span>
            </a>
            <a
              href="https://t.me/{{store_info.telegram"
              target="_blank"
              className="text-slate-500 hover:text-slate-900 dark:hover:text-white"
            >
              {/* <!-- Telegram --> */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 24 24"
                style={{ color: "#0088cc" }}
              >
                <path d="M18.384,22.779c0.322,0.228 0.737,0.285 1.107,0.145c0.37,-0.141 0.642,-0.457 0.724,-0.84c0.869,-4.084 2.977,-14.421 3.768,-18.136c0.06,-0.28 -0.04,-0.571 -0.26,-0.758c-0.22,-0.187 -0.525,-0.241 -0.797,-0.14c-4.193,1.552 -17.106,6.397 -22.384,8.35c-0.335,0.124 -0.553,0.446 -0.542,0.799c0.012,0.354 0.25,0.661 0.593,0.764c2.367,0.708 5.474,1.693 5.474,1.693c0,0 1.452,4.385 2.209,6.615c0.095,0.28 0.314,0.5 0.603,0.576c0.288,0.075 0.596,-0.004 0.811,-0.207c1.216,-1.148 3.096,-2.923 3.096,-2.923c0,0 3.572,2.619 5.598,4.062Zm-11.01,-8.677l1.679,5.538l0.373,-3.507c0,0 6.487,-5.851 10.185,-9.186c0.108,-0.098 0.123,-0.262 0.033,-0.377c-0.089,-0.115 -0.253,-0.142 -0.376,-0.064c-4.286,2.737 -11.894,7.596 -11.894,7.596Z" />
              </svg>
              <span className="sr-only">Telegram</span>
            </a>
          </div>
        </div>
      </div>

      {/* <!-- End Footer --> */}
    </footer>
  );
};

export default Footer;
