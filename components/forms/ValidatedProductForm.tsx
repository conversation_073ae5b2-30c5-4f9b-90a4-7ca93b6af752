"use client";

import { useState } from "react";
import {
  FormCard,
  FormGrid,
  FormSection,
  FormActions,
  Input,
  Textarea,
  CategorySelect,
  NumberInput,
  Checkbox,
  SubmitButton,
  CancelButton,
} from "@/lib/forms";
import { ProductFormSchema, type ProductForm } from "@/lib/dto";
import { useValidatedForm, createFieldProps, createNumberFieldProps } from "@/lib/form-validation";

interface ValidatedProductFormProps {
  initialData?: Partial<ProductForm>;
  onSubmit: (data: ProductForm) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

export default function ValidatedProductForm({
  initialData = {},
  onSubmit,
  onCancel,
  isLoading = false,
}: ValidatedProductFormProps) {
  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    validateField,
    submitForm,
    resetForm,
  } = useValidatedForm(ProductFormSchema, {
    name: "",
    description: "",
    barcode: "",
    sku: "",
    price: "",
    cost: "",
    stock: "0",
    minStock: "0",
    categoryId: "",
    isActive: true,
    ...initialData,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await submitForm(onSubmit);
    if (result.success) {
      // Form submitted successfully
      console.log("Product saved:", result.data);
    }
  };

  const handleReset = () => {
    resetForm();
  };

  return (
    <FormCard
      title="Product Information"
      description="Enter the product details below"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <FormSection
          title="Basic Information"
          description="Essential product details"
        >
          <FormGrid cols={2} gap="md">
            <div className="sm:col-span-2">
              <Input
                {...createFieldProps("name", formData, errors, updateField, validateField)}
                label="Product Name"
                placeholder="Enter product name"
                required
                helpText="The display name for this product"
              />
            </div>

            <Input
              {...createFieldProps("sku", formData, errors, updateField, validateField)}
              label="SKU"
              placeholder="Product SKU"
              helpText="Unique product identifier"
            />

            <Input
              {...createFieldProps("barcode", formData, errors, updateField, validateField)}
              label="Barcode"
              placeholder="Product barcode"
              helpText="Barcode for scanning"
            />

            <div className="sm:col-span-2">
              <Textarea
                {...createFieldProps("description", formData, errors, updateField, validateField)}
                label="Description"
                placeholder="Product description"
                rows={3}
                helpText="Detailed product description"
              />
            </div>
          </FormGrid>
        </FormSection>

        {/* Pricing & Inventory */}
        <FormSection
          title="Pricing & Inventory"
          description="Set pricing and stock levels"
        >
          <FormGrid cols={2} gap="md">
            <Input
              {...createNumberFieldProps("price", formData, errors, updateField, validateField)}
              label="Price"
              placeholder="0.00"
              required
              helpText="Selling price"
            />

            <Input
              {...createNumberFieldProps("cost", formData, errors, updateField, validateField)}
              label="Cost"
              placeholder="0.00"
              helpText="Cost price (optional)"
            />

            <Input
              {...createNumberFieldProps("stock", formData, errors, updateField, validateField)}
              label="Initial Stock"
              placeholder="0"
              required
              helpText="Starting inventory quantity"
            />

            <Input
              {...createNumberFieldProps("minStock", formData, errors, updateField, validateField)}
              label="Minimum Stock"
              placeholder="0"
              helpText="Low stock alert threshold"
            />
          </FormGrid>
        </FormSection>

        {/* Category & Settings */}
        <FormSection
          title="Category & Settings"
          description="Product categorization and status"
        >
          <FormGrid cols={2} gap="md">
            <CategorySelect
              name="categoryId"
              label="Category"
              value={formData.categoryId || ""}
              onChange={(e) => updateField("categoryId", e.target.value)}
              error={errors.categoryId}
              required
              helpText="Product category"
            />

            <div className="flex items-center">
              <Checkbox
                name="isActive"
                label="Active Product"
                checked={formData.isActive || false}
                onChange={(e) => updateField("isActive", e.target.checked)}
                helpText="Whether this product is available for sale"
              />
            </div>
          </FormGrid>
        </FormSection>

        {/* Form Actions */}
        <FormActions>
          <div className="flex justify-between w-full">
            <div className="flex gap-3">
              <CancelButton
                type="button"
                onClick={onCancel || handleReset}
                disabled={isSubmitting || isLoading}
              >
                Cancel
              </CancelButton>
            </div>
            
            <div className="flex gap-3">
              <button
                type="button"
                onClick={handleReset}
                disabled={isSubmitting || isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                Reset
              </button>
              
              <SubmitButton
                loading={isSubmitting || isLoading}
                disabled={isSubmitting || isLoading}
              >
                {isSubmitting ? "Saving..." : "Save Product"}
              </SubmitButton>
            </div>
          </div>
        </FormActions>

        {/* Display validation errors */}
        {Object.keys(errors).length > 0 && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Please fix the following errors:
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <ul className="list-disc space-y-1 pl-5">
                    {Object.entries(errors).map(([field, message]) => (
                      <li key={field}>
                        <strong>{field}:</strong> {message}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </form>
    </FormCard>
  );
}
