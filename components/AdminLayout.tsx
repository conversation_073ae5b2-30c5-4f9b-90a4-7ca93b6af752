// components/Layout/AdminLayout.tsx
"use client";

import { ReactNode, useState } from "react";
import { SidebarNav, TopNavbar } from "./Navs;

export const AdminLayout = ({ children }: { children: ReactNode }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleToggleSidebar = () => {
    setSidebarOpen((prev) => !prev);
  };

  return (
    <>
      <TopNavbar onToggleSidebar={handleToggleSidebar} />
      <SidebarNav />
      <main className="p-4 lg:ml-64 pt-20 bg-gray-50 dark:bg-gray-900 min-h-screen">
        {children}
      </main>
    </>
  );
};
