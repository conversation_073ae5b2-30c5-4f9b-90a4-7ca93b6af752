"use client";

import {
  CancelButton,
  FormActions,
  FormCard,
  FormGrid,
  Input,
  Select,
  SubmitButton,
} from "@/lib/forms";
import Link from "next/link";
import { useState } from "react";

interface UserFormData {
  name: string;
  email: string;
  role: "ADMIN" | "MANAGER" | "CASHIER";
}

export default function NewUserPage() {
  const [formData, setFormData] = useState<UserFormData>({
    name: "",
    email: "",
    role: "CASHIER",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const roleOptions = [
    { value: "CASHIER", label: "Cashier" },
    { value: "MANAGER", label: "Manager" },
    { value: "ADMIN", label: "Admin" },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    // Basic validation
    const newErrors: Record<string, string> = {};
    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!/\S+@\S+\.\S+/.test(formData.email))
      newErrors.email = "Email is invalid";

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch("/api/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.fieldErrors) {
          setErrors(errorData.fieldErrors);
        } else {
          setErrors({ general: errorData.error || "Failed to create user" });
        }
        return;
      }

      // Success - redirect to users list
      window.location.href = "/users";
    } catch {
      setErrors({ general: "Failed to create user" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof UserFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/users"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Users
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="h-5 w-5 flex-shrink-0 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      New User
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Add New User
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Create a new user account for the system
            </p>
          </div>
        </div>
      </div>

      {/* User Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {errors.general && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors.general}
            </p>
          </div>
        )}

        <FormCard
          title="User Information"
          description="Basic user account details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="name"
              label="Full Name"
              placeholder="Enter full name"
              required
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              error={errors.name}
            />

            <Input
              name="email"
              label="Email Address"
              type="email"
              placeholder="Enter email address"
              required
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              error={errors.email}
            />

            <div className="sm:col-span-2">
              <Select
                name="role"
                label="Role"
                options={roleOptions}
                value={formData.role}
                onChange={(e) =>
                  handleInputChange(
                    "role",
                    e.target.value as UserFormData["role"],
                  )
                }
                error={errors.role}
                helpText="Select the user's role in the system"
              />
            </div>
          </FormGrid>
        </FormCard>

        <FormActions align="right">
          <div className="flex space-x-3">
            <Link href="/users">
              <CancelButton>Cancel</CancelButton>
            </Link>
            <SubmitButton loading={isSubmitting} disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create User"}
            </SubmitButton>
          </div>
        </FormActions>
      </form>
    </div>
  );
}
