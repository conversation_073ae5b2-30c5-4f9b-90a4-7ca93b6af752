"use client";

import Link from "next/link";
import { useState } from "react";
import {
  CancelButton,
  CategorySelect,
  Checkbox,
  CurrencyInput,
  FormActions,
  FormCard,
  FormGrid,
  FormSection,
  Input,
  NumberInput,
  SubmitButton,
  Textarea,
} from "../../../../lib/forms";

export default function NewUserPage() {
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    price: 0,
    stock: 0,
    sku: "",
    description: "",
    barcode: "",
    supplier: "",
    costPrice: 0,
    weight: "",
    expiryDate: "",
    isActive: true,
    isFeatured: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    // Basic validation
    const newErrors: Record<string, string> = {};
    if (!formData.name.trim()) newErrors.name = "User name is required";
    if (!formData.sku.trim()) newErrors.sku = "SKU is required";
    if (!formData.category) newErrors.category = "Category is required";
    if (formData.price <= 0) newErrors.price = "Price must be greater than 0";
    if (formData.costPrice <= 0)
      newErrors.costPrice = "Cost price must be greater than 0";

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("User created:", formData);
      // Handle success (redirect to users list or show success message)
      // router.push('/users');
    } catch {
      setErrors({ general: "Failed to create user" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (
    field: string,
    value: string | number | boolean,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/users"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Users
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="h-5 w-5 flex-shrink-0 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      New User
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Add New User
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Create a new user for your inventory
            </p>
          </div>
        </div>
      </div>

      {/* User Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {errors.general && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors.general}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main User Information */}
          <div className="lg:col-span-2">
            <FormSection
              title="User Information"
              description="Basic user details and description"
            >
              <FormGrid cols={2} gap="md">
                <div className="sm:col-span-2">
                  <Input
                    name="name"
                    label="User Name"
                    placeholder="Enter user name"
                    required
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    error={errors.name}
                  />
                </div>

                <Input
                  name="sku"
                  label="SKU"
                  placeholder="User SKU"
                  required
                  value={formData.sku}
                  onChange={(e) => handleInputChange("sku", e.target.value)}
                  error={errors.sku}
                  helpText="Unique user identifier"
                />

                <Input
                  name="barcode"
                  label="Barcode"
                  placeholder="User barcode"
                  value={formData.barcode}
                  onChange={(e) => handleInputChange("barcode", e.target.value)}
                  error={errors.barcode}
                />

                <CategorySelect
                  name="category"
                  label="Category"
                  required
                  value={formData.category}
                  onChange={(e) =>
                    handleInputChange("category", e.target.value)
                  }
                  error={errors.category}
                />

                <Input
                  name="supplier"
                  label="Supplier"
                  placeholder="Supplier name"
                  value={formData.supplier}
                  onChange={(e) =>
                    handleInputChange("supplier", e.target.value)
                  }
                  error={errors.supplier}
                />
              </FormGrid>

              <Textarea
                name="description"
                label="Description"
                placeholder="User description"
                rows={3}
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                error={errors.description}
                helpText="Provide a detailed description of the user"
              />
            </FormSection>
          </div>

          {/* Pricing and Inventory */}
          <div className="space-y-8">
            <FormCard title="Pricing" description="Set user pricing">
              <div className="space-y-4">
                <CurrencyInput
                  name="costPrice"
                  label="Cost Price"
                  required
                  value={formData.costPrice}
                  onChange={(value) =>
                    handleInputChange("costPrice", value || 0)
                  }
                  error={errors.costPrice}
                  helpText="Your cost for this user"
                />

                <CurrencyInput
                  name="price"
                  label="Selling Price"
                  required
                  value={formData.price}
                  onChange={(value) => handleInputChange("price", value || 0)}
                  error={errors.price}
                  helpText="Customer price"
                />
              </div>
            </FormCard>

            <FormCard title="Inventory" description="Stock and user details">
              <div className="space-y-4">
                <NumberInput
                  name="stock"
                  label="Initial Stock"
                  required
                  min={0}
                  value={formData.stock.toString()}
                  onChange={(e) =>
                    handleInputChange("stock", parseInt(e.target.value) || 0)
                  }
                  error={errors.stock}
                  helpText="Starting inventory quantity"
                />

                <Input
                  name="weight"
                  label="Weight/Size"
                  placeholder="e.g., 1 lb, 500g"
                  value={formData.weight}
                  onChange={(e) => handleInputChange("weight", e.target.value)}
                  error={errors.weight}
                />

                <Input
                  name="expiryDate"
                  label="Expiry Date"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) =>
                    handleInputChange("expiryDate", e.target.value)
                  }
                  error={errors.expiryDate}
                  helpText="Leave empty if user doesn't expire"
                />
              </div>
            </FormCard>

            <FormCard
              title="Settings"
              description="User visibility and status"
            >
              <div className="space-y-4">
                <Checkbox
                  name="isActive"
                  label="Active User"
                  checked={formData.isActive}
                  onChange={(e) =>
                    handleInputChange("isActive", e.target.checked)
                  }
                  helpText="User is available for sale"
                />

                <Checkbox
                  name="isFeatured"
                  label="Featured User"
                  checked={formData.isFeatured}
                  onChange={(e) =>
                    handleInputChange("isFeatured", e.target.checked)
                  }
                  helpText="Show in featured users section"
                />
              </div>
            </FormCard>
          </div>
        </div>

        <FormActions align="right">
          <div className="flex space-x-3">
            <Link href="/users">
              <CancelButton>Cancel</CancelButton>
            </Link>
            <SubmitButton loading={isSubmitting} disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create User"}
            </SubmitButton>
          </div>
        </FormActions>
      </form>
    </div>
  );
}
