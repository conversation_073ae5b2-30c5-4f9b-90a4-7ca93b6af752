"use client";

import { useState, useEffect } from "react";
import {
  FormCard,
  FormGrid,
  FormSection,
  FormActions,
  Input,
  EmailInput,
  PasswordInput,
  NumberInput,
  Textarea,
  CategorySelect,
  Select,
  Checkbox,
  SubmitButton,
  CancelButton,
  Button,
} from "@/lib/forms";
import {
  ProductFormSchema,
  UserFormSchema,
  type ProductForm,
  type UserForm,
} from "@/lib/dto";
import { useValidatedForm } from "@/lib/form-validation";

export default function ImprovedFormsPage() {
  const [activeDemo, setActiveDemo] = useState<"password" | "product" | "user">("password");

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Improved Forms Library</h1>
          <p className="mt-2 text-gray-600">
            Demonstrating the enhanced forms library with password visibility toggle, 
            DTO validation, and improved components.
          </p>
        </div>

        {/* Demo Navigation */}
        <div className="mb-6">
          <nav className="flex space-x-8" aria-label="Demos">
            {[
              { id: "password", name: "Password Input Demo" },
              { id: "product", name: "Product Form with Validation" },
              { id: "user", name: "User Form with Validation" },
            ].map((demo) => (
              <button
                key={demo.id}
                onClick={() => setActiveDemo(demo.id as any)}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeDemo === demo.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {demo.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Demo Content */}
        {activeDemo === "password" && <PasswordDemo />}
        {activeDemo === "product" && <ProductFormDemo />}
        {activeDemo === "user" && <UserFormDemo />}
      </div>
    </div>
  );
}

// Password Input Demo
function PasswordDemo() {
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const newErrors: Record<string, string> = {};
    
    if (!formData.currentPassword) {
      newErrors.currentPassword = "Current password is required";
    }
    
    if (!formData.newPassword) {
      newErrors.newPassword = "New password is required";
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = "Password must be at least 8 characters";
    }
    
    if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      alert("Password changed successfully!");
      setFormData({ currentPassword: "", newPassword: "", confirmPassword: "" });
    }
  };

  return (
    <FormCard title="Change Password" description="Update your account password">
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <PasswordInput
            name="currentPassword"
            label="Current Password"
            placeholder="Enter current password"
            required
            value={formData.currentPassword}
            onChange={(e) => setFormData(prev => ({ ...prev, currentPassword: e.target.value }))}
            error={errors.currentPassword}
          />

          <PasswordInput
            name="newPassword"
            label="New Password"
            placeholder="Enter new password"
            required
            value={formData.newPassword}
            onChange={(e) => setFormData(prev => ({ ...prev, newPassword: e.target.value }))}
            error={errors.newPassword}
            helpText="Must be at least 8 characters long"
          />

          <PasswordInput
            name="confirmPassword"
            label="Confirm New Password"
            placeholder="Confirm new password"
            required
            value={formData.confirmPassword}
            onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
            error={errors.confirmPassword}
          />
        </div>

        <FormActions>
          <CancelButton 
            onClick={() => setFormData({ currentPassword: "", newPassword: "", confirmPassword: "" })}
          >
            Cancel
          </CancelButton>
          <SubmitButton>Change Password</SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}

// Product Form Demo with DTO Validation
function ProductFormDemo() {
  const [categories, setCategories] = useState<Array<{value: string, label: string}>>([]);

  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    validateField,
    submitForm,
    resetForm,
  } = useValidatedForm(ProductFormSchema, {
    name: "",
    description: "",
    barcode: "",
    sku: "",
    price: "",
    cost: "",
    stock: "0",
    minStock: "0",
    categoryId: "",
    isActive: true,
  });

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        if (response.ok) {
          const data = await response.json();
          const categoryOptions = data.map((cat: any) => ({
            value: cat.id,
            label: cat.name,
          }));
          setCategories(categoryOptions);
        }
      } catch (error) {
        console.error('Failed to fetch categories:', error);
      }
    };

    fetchCategories();
  }, []);

  const handleSubmit = async (data: ProductForm) => {
    console.log("Product form submitted:", data);
    await new Promise((resolve) => setTimeout(resolve, 1000));
    alert("Product saved successfully!");
    resetForm();
  };

  return (
    <FormCard title="Create Product" description="Add a new product with full validation">
      <form onSubmit={(e) => { e.preventDefault(); submitForm(handleSubmit); }}>
        <FormSection title="Basic Information">
          <FormGrid cols={2}>
            <div className="sm:col-span-2">
              <Input
                name="name"
                label="Product Name"
                placeholder="Enter product name"
                required
                value={formData.name || ""}
                onChange={(e) => updateField("name", e.target.value)}
                onBlur={() => validateField("name", formData.name)}
                error={errors.name}
              />
            </div>
            
            <Input
              name="sku"
              label="SKU"
              placeholder="Product SKU"
              value={formData.sku || ""}
              onChange={(e) => updateField("sku", e.target.value)}
              error={errors.sku}
            />
            
            <Input
              name="barcode"
              label="Barcode"
              placeholder="Product barcode"
              value={formData.barcode || ""}
              onChange={(e) => updateField("barcode", e.target.value)}
              error={errors.barcode}
            />
            
            <div className="sm:col-span-2">
              <Textarea
                name="description"
                label="Description"
                placeholder="Product description"
                rows={3}
                value={formData.description || ""}
                onChange={(e) => updateField("description", e.target.value)}
                error={errors.description}
              />
            </div>
          </FormGrid>
        </FormSection>

        <FormSection title="Pricing & Inventory">
          <FormGrid cols={2}>
            <Input
              name="price"
              label="Price"
              placeholder="0.00"
              required
              value={formData.price || ""}
              onChange={(e) => updateField("price", e.target.value)}
              error={errors.price}
            />
            
            <Input
              name="cost"
              label="Cost"
              placeholder="0.00"
              value={formData.cost || ""}
              onChange={(e) => updateField("cost", e.target.value)}
              error={errors.cost}
            />
            
            <Input
              name="stock"
              label="Stock"
              placeholder="0"
              value={formData.stock || ""}
              onChange={(e) => updateField("stock", e.target.value)}
              error={errors.stock}
            />
            
            <Input
              name="minStock"
              label="Min Stock"
              placeholder="0"
              value={formData.minStock || ""}
              onChange={(e) => updateField("minStock", e.target.value)}
              error={errors.minStock}
            />
          </FormGrid>
        </FormSection>

        <FormSection title="Category & Settings">
          <FormGrid cols={2}>
            <CategorySelect
              name="categoryId"
              label="Category"
              categories={categories}
              value={formData.categoryId || ""}
              onChange={(e) => updateField("categoryId", e.target.value)}
              error={errors.categoryId}
              required
            />
            
            <div className="flex items-center">
              <Checkbox
                name="isActive"
                label="Active Product"
                checked={formData.isActive || false}
                onChange={(e) => updateField("isActive", e.target.checked)}
              />
            </div>
          </FormGrid>
        </FormSection>

        <FormActions>
          <CancelButton onClick={resetForm}>Reset</CancelButton>
          <SubmitButton loading={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Product"}
          </SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}

// User Form Demo
function UserFormDemo() {
  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    submitForm,
    resetForm,
  } = useValidatedForm(UserFormSchema, {
    email: "",
    name: "",
    role: "CASHIER",
  });

  const handleSubmit = async (data: UserForm) => {
    console.log("User form submitted:", data);
    await new Promise((resolve) => setTimeout(resolve, 1000));
    alert("User saved successfully!");
  };

  const roleOptions = [
    { value: "ADMIN", label: "Administrator" },
    { value: "MANAGER", label: "Manager" },
    { value: "CASHIER", label: "Cashier" },
  ];

  return (
    <FormCard title="Create User" description="Add a new user account">
      <form onSubmit={(e) => { e.preventDefault(); submitForm(handleSubmit); }}>
        <FormGrid cols={2}>
          <Input
            name="name"
            label="Full Name"
            placeholder="Enter full name"
            required
            value={formData.name || ""}
            onChange={(e) => updateField("name", e.target.value)}
            error={errors.name}
          />
          
          <EmailInput
            name="email"
            label="Email"
            placeholder="Enter email address"
            required
            value={formData.email || ""}
            onChange={(e) => updateField("email", e.target.value)}
            error={errors.email}
          />
          
          <Select
            name="role"
            label="Role"
            value={formData.role || ""}
            onChange={(e) => updateField("role", e.target.value)}
            error={errors.role}
            options={roleOptions}
            required
          />
        </FormGrid>

        <FormActions>
          <CancelButton onClick={resetForm}>Reset</CancelButton>
          <SubmitButton loading={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save User"}
          </SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}
