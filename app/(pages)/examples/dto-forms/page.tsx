"use client";

import { useState } from "react";
import {
  FormCard,
  FormGrid,
  FormSection,
  FormActions,
  Input,
  Textarea,
  CategorySelect,
  Select,
  Checkbox,
  SubmitButton,
  CancelButton,
} from "@/lib/forms";
import {
  ProductFormSchema,
  CategoryFormSchema,
  UserFormSchema,
  type ProductForm,
  type CategoryForm,
  type UserForm,
} from "@/lib/dto";
import { useValidatedForm, createFieldProps, createNumberFieldProps } from "@/lib/form-validation";

export default function DTOFormsExamplePage() {
  const [activeTab, setActiveTab] = useState<"product" | "category" | "user">("product");

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">DTO Forms Examples</h1>
          <p className="mt-2 text-gray-600">
            Examples of forms using Zod validation with custom DTOs for type safety and validation.
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <nav className="flex space-x-8" aria-label="Tabs">
            {[
              { id: "product", name: "Product Form" },
              { id: "category", name: "Category Form" },
              { id: "user", name: "User Form" },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === "product" && <ProductFormExample />}
        {activeTab === "category" && <CategoryFormExample />}
        {activeTab === "user" && <UserFormExample />}
      </div>
    </div>
  );
}

// Product Form Example
function ProductFormExample() {
  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    validateField,
    submitForm,
    resetForm,
  } = useValidatedForm(ProductFormSchema, {
    name: "",
    description: "",
    barcode: "",
    sku: "",
    price: "",
    cost: "",
    stock: "0",
    minStock: "0",
    categoryId: "",
    isActive: true,
  });

  const handleSubmit = async (data: ProductForm) => {
    console.log("Product form submitted:", data);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    alert("Product saved successfully!");
  };

  return (
    <FormCard title="Create Product" description="Add a new product with validation">
      <form onSubmit={(e) => { e.preventDefault(); submitForm(handleSubmit); }}>
        <FormSection title="Basic Information">
          <FormGrid cols={2}>
            <div className="sm:col-span-2">
              <Input
                {...createFieldProps("name", formData, errors, updateField, validateField)}
                label="Product Name"
                placeholder="Enter product name"
                required
              />
            </div>
            
            <Input
              {...createFieldProps("sku", formData, errors, updateField, validateField)}
              label="SKU"
              placeholder="Product SKU"
            />
            
            <Input
              {...createFieldProps("barcode", formData, errors, updateField, validateField)}
              label="Barcode"
              placeholder="Product barcode"
            />
            
            <div className="sm:col-span-2">
              <Textarea
                {...createFieldProps("description", formData, errors, updateField, validateField)}
                label="Description"
                placeholder="Product description"
                rows={3}
              />
            </div>
          </FormGrid>
        </FormSection>

        <FormSection title="Pricing & Inventory">
          <FormGrid cols={2}>
            <Input
              {...createNumberFieldProps("price", formData, errors, updateField, validateField)}
              label="Price"
              placeholder="0.00"
              required
            />
            
            <Input
              {...createNumberFieldProps("cost", formData, errors, updateField, validateField)}
              label="Cost"
              placeholder="0.00"
            />
            
            <Input
              {...createNumberFieldProps("stock", formData, errors, updateField, validateField)}
              label="Stock"
              placeholder="0"
            />
            
            <Input
              {...createNumberFieldProps("minStock", formData, errors, updateField, validateField)}
              label="Min Stock"
              placeholder="0"
            />
          </FormGrid>
        </FormSection>

        <FormSection title="Category & Settings">
          <FormGrid cols={2}>
            <CategorySelect
              name="categoryId"
              label="Category"
              value={formData.categoryId || ""}
              onChange={(e) => updateField("categoryId", e.target.value)}
              error={errors.categoryId}
              required
            />
            
            <Checkbox
              name="isActive"
              label="Active Product"
              checked={formData.isActive || false}
              onChange={(e) => updateField("isActive", e.target.checked)}
            />
          </FormGrid>
        </FormSection>

        <FormActions>
          <CancelButton onClick={resetForm}>Reset</CancelButton>
          <SubmitButton loading={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Product"}
          </SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}

// Category Form Example
function CategoryFormExample() {
  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    validateField,
    submitForm,
    resetForm,
  } = useValidatedForm(CategoryFormSchema, {
    name: "",
    description: "",
  });

  const handleSubmit = async (data: CategoryForm) => {
    console.log("Category form submitted:", data);
    await new Promise((resolve) => setTimeout(resolve, 1000));
    alert("Category saved successfully!");
  };

  return (
    <FormCard title="Create Category" description="Add a new product category">
      <form onSubmit={(e) => { e.preventDefault(); submitForm(handleSubmit); }}>
        <FormGrid cols={1}>
          <Input
            {...createFieldProps("name", formData, errors, updateField, validateField)}
            label="Category Name"
            placeholder="Enter category name"
            required
          />
          
          <Textarea
            {...createFieldProps("description", formData, errors, updateField, validateField)}
            label="Description"
            placeholder="Category description"
            rows={3}
          />
        </FormGrid>

        <FormActions>
          <CancelButton onClick={resetForm}>Reset</CancelButton>
          <SubmitButton loading={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Category"}
          </SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}

// User Form Example
function UserFormExample() {
  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    validateField,
    submitForm,
    resetForm,
  } = useValidatedForm(UserFormSchema, {
    email: "",
    name: "",
    role: "CASHIER",
  });

  const handleSubmit = async (data: UserForm) => {
    console.log("User form submitted:", data);
    await new Promise((resolve) => setTimeout(resolve, 1000));
    alert("User saved successfully!");
  };

  const roleOptions = [
    { value: "ADMIN", label: "Administrator" },
    { value: "MANAGER", label: "Manager" },
    { value: "CASHIER", label: "Cashier" },
  ];

  return (
    <FormCard title="Create User" description="Add a new user account">
      <form onSubmit={(e) => { e.preventDefault(); submitForm(handleSubmit); }}>
        <FormGrid cols={2}>
          <Input
            {...createFieldProps("name", formData, errors, updateField, validateField)}
            label="Full Name"
            placeholder="Enter full name"
            required
          />
          
          <Input
            {...createFieldProps("email", formData, errors, updateField, validateField)}
            label="Email"
            type="email"
            placeholder="Enter email address"
            required
          />
          
          <Select
            name="role"
            label="Role"
            value={formData.role || ""}
            onChange={(e) => updateField("role", e.target.value)}
            error={errors.role}
            options={roleOptions}
            required
          />
        </FormGrid>

        <FormActions>
          <CancelButton onClick={resetForm}>Reset</CancelButton>
          <SubmitButton loading={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save User"}
          </SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}
