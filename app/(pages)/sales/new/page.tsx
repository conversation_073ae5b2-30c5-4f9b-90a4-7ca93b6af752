"use client";

import Link from "next/link";
import { useState } from "react";
import {
  FormCard,
  FormSection,
  FormGrid,
  FormActions,
  Input,
  SearchInput,
  Select,
  CurrencyInput,
  NumberInput,
  Button,
  SubmitButton,
  CancelButton,
} from "../../../../lib/forms";

// Mock products for search
const products = [
  { id: 1, name: "Organic Bananas", sku: "ORG-BAN-001", price: 2.99, stock: 150 },
  { id: 2, name: "Whole Milk", sku: "MILK-WHL-001", price: 3.49, stock: 45 },
  { id: 3, name: "Bread - Whole Wheat", sku: "BRD-WHT-001", price: 2.79, stock: 8 },
  { id: 4, name: "Ground Coffee", sku: "COF-GRD-001", price: 12.99, stock: 0 },
];

interface CartItem {
  id: number;
  name: string;
  sku: string;
  price: number;
  quantity: number;
  total: number;
}

export default function NewSalePage() {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [customerInfo, setCustomerInfo] = useState({
    name: "",
    email: "",
    phone: "",
  });
  const [paymentMethod, setPaymentMethod] = useState("cash");
  const [searchQuery, setSearchQuery] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const paymentOptions = [
    { value: "cash", label: "Cash" },
    { value: "credit", label: "Credit Card" },
    { value: "debit", label: "Debit Card" },
    { value: "mobile", label: "Mobile Payment" },
  ];

  const addToCart = (product: typeof products[0]) => {
    const existingItem = cart.find(item => item.id === product.id);
    
    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1, total: (item.quantity + 1) * item.price }
          : item
      ));
    } else {
      setCart([...cart, {
        id: product.id,
        name: product.name,
        sku: product.sku,
        price: product.price,
        quantity: 1,
        total: product.price,
      }]);
    }
  };

  const updateQuantity = (id: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(id);
      return;
    }

    setCart(cart.map(item =>
      item.id === id
        ? { ...item, quantity, total: quantity * item.price }
        : item
    ));
  };

  const removeFromCart = (id: number) => {
    setCart(cart.filter(item => item.id !== id));
  };

  const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
  const tax = subtotal * 0.08; // 8% tax
  const total = subtotal + tax;

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // In a real app, this would filter products or search the database
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (cart.length === 0) {
      alert("Please add items to the cart");
      return;
    }

    setIsProcessing(true);
    
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));
      
      const saleData = {
        items: cart,
        customer: customerInfo,
        paymentMethod,
        subtotal,
        tax,
        total,
        timestamp: new Date().toISOString(),
      };
      
      console.log("Sale completed:", saleData);
      
      // Reset form
      setCart([]);
      setCustomerInfo({ name: "", email: "", phone: "" });
      setPaymentMethod("cash");
      
      alert("Sale completed successfully!");
      
    } catch (error) {
      console.error("Error processing sale:", error);
      alert("Error processing sale. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/sales"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Sales
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="flex-shrink-0 h-5 w-5 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      New Sale
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              New Sale
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Process a new sale transaction
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Product Search & Cart */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Search */}
            <FormCard title="Add Products" description="Search and add products to the sale">
              <SearchInput
                name="productSearch"
                label="Search Products"
                placeholder="Search by name or SKU..."
                value={searchQuery}
                onSearch={handleSearch}
                debounceMs={300}
                showClearButton
              />

              {searchQuery && (
                <div className="mt-4 space-y-2">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">Search Results:</h4>
                  <div className="space-y-2">
                    {filteredProducts.map((product) => (
                      <div
                        key={product.id}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-md dark:border-gray-600"
                      >
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {product.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            SKU: {product.sku} | Stock: {product.stock} | ${product.price}
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="primary"
                          size="sm"
                          onClick={() => addToCart(product)}
                          disabled={product.stock === 0}
                        >
                          {product.stock === 0 ? "Out of Stock" : "Add"}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </FormCard>

            {/* Shopping Cart */}
            <FormCard title="Shopping Cart" description="Items in current sale">
              {cart.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                  No items in cart. Search and add products above.
                </p>
              ) : (
                <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                          Product
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                          Price
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                          Quantity
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                          Total
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                      {cart.map((item) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {item.name}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                SKU: {item.sku}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            ${item.price.toFixed(2)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <NumberInput
                              name={`quantity-${item.id}`}
                              value={item.quantity.toString()}
                              onChange={(e) => updateQuantity(item.id, parseInt(e.target.value) || 0)}
                              min={0}
                              className="w-20"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            ${item.total.toFixed(2)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              type="button"
                              onClick={() => removeFromCart(item.id)}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </FormCard>
          </div>

          {/* Customer & Payment */}
          <div className="space-y-6">
            <FormCard title="Customer Information" description="Optional customer details">
              <div className="space-y-4">
                <Input
                  name="customerName"
                  label="Customer Name"
                  placeholder="Enter customer name"
                  value={customerInfo.name}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                />

                <Input
                  name="customerEmail"
                  label="Email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={customerInfo.email}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                />

                <Input
                  name="customerPhone"
                  label="Phone"
                  placeholder="+****************"
                  value={customerInfo.phone}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                />
              </div>
            </FormCard>

            <FormCard title="Payment" description="Payment method and totals">
              <div className="space-y-4">
                <Select
                  name="paymentMethod"
                  label="Payment Method"
                  options={paymentOptions}
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  required
                />

                <div className="border-t border-gray-200 pt-4 dark:border-gray-600">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Subtotal:</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        ${subtotal.toFixed(2)}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Tax (8%):</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        ${tax.toFixed(2)}
                      </span>
                    </div>

                    <div className="border-t border-gray-200 pt-2 dark:border-gray-600">
                      <div className="flex justify-between">
                        <span className="text-lg font-medium text-gray-900 dark:text-white">Total:</span>
                        <span className="text-lg font-bold text-gray-900 dark:text-white">
                          ${total.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </FormCard>
          </div>
        </div>

        <FormActions align="right">
          <div className="flex space-x-3">
            <Link href="/sales">
              <CancelButton>Cancel</CancelButton>
            </Link>
            <SubmitButton 
              loading={isProcessing} 
              disabled={cart.length === 0 || isProcessing}
            >
              {isProcessing ? "Processing..." : `Complete Sale ($${total.toFixed(2)})`}
            </SubmitButton>
          </div>
        </FormActions>
      </form>
    </div>
  );
}
