"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import {
  FormCard,
  FormSection,
  FormGrid,
  FormActions,
  Input,
  Select,
  CurrencyInput,
  NumberInput,
  Button,
  SubmitButton,
  CancelButton,
} from "../../../../lib/forms";

// Mock sale data
async function getSale(id: string) {
  return {
    id: id,
    date: "2024-01-15",
    time: "14:30",
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
    },
    items: [
      {
        id: 1,
        name: "Organic Bananas",
        sku: "ORG-BAN-001",
        quantity: 2,
        unitPrice: 2.99,
        total: 5.98,
      },
      {
        id: 2,
        name: "Whole Milk",
        sku: "MILK-WHL-001",
        quantity: 1,
        unitPrice: 3.49,
        total: 3.49,
      },
      {
        id: 3,
        name: "Bread - Whole Wheat",
        sku: "BRD-WHT-001",
        quantity: 1,
        unitPrice: 2.79,
        total: 2.79,
      },
    ],
    subtotal: 12.26,
    tax: 0.98,
    discount: 0.00,
    total: 13.24,
    paymentMethod: "Credit Card",
    paymentDetails: {
      cardType: "Visa",
      lastFour: "1234",
      authCode: "ABC123",
    },
    status: "Completed",
    cashier: "Alice Johnson",
    notes: "",
  };
}

export default function SaleDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const [sale, setSale] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefunding, setIsRefunding] = useState(false);

  useEffect(() => {
    const loadSale = async () => {
      try {
        const saleData = await getSale(params.id);
        setSale(saleData);
      } catch (error) {
        console.error("Error loading sale:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSale();
  }, [params.id]);

  const handleRefund = async () => {
    if (confirm("Are you sure you want to refund this sale?")) {
      setIsRefunding(true);
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));
        console.log("Sale refunded:", params.id);
        // Update sale status or redirect
      } catch (error) {
        console.error("Error refunding sale:", error);
      } finally {
        setIsRefunding(false);
      }
    }
  };

  const handlePrintReceipt = () => {
    console.log("Printing receipt for sale:", params.id);
    // Implement receipt printing logic
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!sale) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Sale not found
        </h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          The sale you're looking for doesn't exist.
        </p>
        <Link
          href="/sales"
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200"
        >
          Back to Sales
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/sales"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Sales
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="flex-shrink-0 h-5 w-5 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      {sale.id}
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Sale Details
            </h1>
          </div>
          <div className="flex space-x-3">
            <Button variant="secondary" onClick={handlePrintReceipt}>
              Print Receipt
            </Button>
            <Button 
              variant="danger" 
              onClick={handleRefund}
              loading={isRefunding}
              disabled={sale.status === "Refunded"}
            >
              {isRefunding ? "Processing..." : "Refund Sale"}
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Sale Information */}
        <div className="lg:col-span-2 space-y-6">
          <FormSection title="Sale Information" description="Transaction details and customer info">
            <FormGrid cols={2} gap="md">
              <Input
                name="saleId"
                label="Transaction ID"
                value={sale.id}
                disabled
              />

              <Input
                name="date"
                label="Date & Time"
                value={`${sale.date} ${sale.time}`}
                disabled
              />

              <Input
                name="customerName"
                label="Customer Name"
                value={sale.customer.name}
                disabled
              />

              <Input
                name="customerEmail"
                label="Customer Email"
                value={sale.customer.email}
                disabled
              />

              <Input
                name="customerPhone"
                label="Customer Phone"
                value={sale.customer.phone}
                disabled
              />

              <Input
                name="cashier"
                label="Cashier"
                value={sale.cashier}
                disabled
              />
            </FormGrid>
          </FormSection>

          {/* Items */}
          <FormSection title="Items Purchased" description="Products in this transaction">
            <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                      Unit Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                  {sale.items.map((item: any) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {item.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            SKU: {item.sku}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        ${item.unitPrice.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        ${item.total.toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </FormSection>
        </div>

        {/* Payment & Summary */}
        <div className="space-y-6">
          <FormCard title="Payment Details" description="Payment method and transaction info">
            <div className="space-y-4">
              <Input
                name="paymentMethod"
                label="Payment Method"
                value={sale.paymentMethod}
                disabled
              />

              {sale.paymentDetails && (
                <>
                  <Input
                    name="cardType"
                    label="Card Type"
                    value={sale.paymentDetails.cardType}
                    disabled
                  />

                  <Input
                    name="lastFour"
                    label="Card Number"
                    value={`****-****-****-${sale.paymentDetails.lastFour}`}
                    disabled
                  />

                  <Input
                    name="authCode"
                    label="Authorization Code"
                    value={sale.paymentDetails.authCode}
                    disabled
                  />
                </>
              )}

              <Input
                name="status"
                label="Status"
                value={sale.status}
                disabled
              />
            </div>
          </FormCard>

          <FormCard title="Sale Summary" description="Transaction totals">
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Subtotal:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  ${sale.subtotal.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Tax:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  ${sale.tax.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Discount:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  -${sale.discount.toFixed(2)}
                </span>
              </div>

              <div className="border-t border-gray-200 pt-4 dark:border-gray-600">
                <div className="flex justify-between">
                  <span className="text-lg font-medium text-gray-900 dark:text-white">Total:</span>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    ${sale.total.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </FormCard>
        </div>
      </div>

      <FormActions align="right">
        <Link href="/sales">
          <CancelButton>Back to Sales</CancelButton>
        </Link>
      </FormActions>
    </div>
  );
}
