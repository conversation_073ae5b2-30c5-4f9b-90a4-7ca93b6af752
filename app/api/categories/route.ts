import { db } from "@/lib/db";
import { CreateCategorySchema } from "@/lib/dto";
import { validateRequestBody } from "@/lib/validation";
import { NextRequest, NextResponse } from "next/server";

export async function GET() {
  try {
    const categories = await db.categories.getAll();
    return NextResponse.json(categories);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      { error: "Failed to fetch categories" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Validate request body using DTO schema
    const validatedData = await validateRequestBody(
      request,
      CreateCategorySchema,
    );

    const category = await db.prisma.category.create({
      data: validatedData,
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error: any) {
    console.error("Error creating category:", error);

    // Handle validation errors
    if (error.statusCode === 400) {
      return NextResponse.json(
        {
          error: "Validation failed",
          errors: error.errors,
          fieldErrors: error.fieldErrors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: "Failed to create category" },
      { status: 500 },
    );
  }
}
