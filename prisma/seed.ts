import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create categories
  const categories = await Promise.all([
    prisma.category.create({
      data: {
        name: 'Beverages',
        description: 'Drinks and beverages',
      },
    }),
    prisma.category.create({
      data: {
        name: 'Snacks',
        description: 'Snacks and confectionery',
      },
    }),
    prisma.category.create({
      data: {
        name: 'Dairy',
        description: 'Dairy products',
      },
    }),
    prisma.category.create({
      data: {
        name: 'Household',
        description: 'Household items and cleaning supplies',
      },
    }),
  ])

  console.log('✅ Categories created')

  // Create admin user
  const adminUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'ADMIN',
    },
  })

  // Create cashier user
  const cashierUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Cashier User',
      role: 'CASHIER',
    },
  })

  console.log('✅ Users created')

  // Create products
  const products = await Promise.all([
    // Beverages
    prisma.product.create({
      data: {
        name: 'Coca Cola 500ml',
        description: 'Refreshing cola drink',
        barcode: '1234567890123',
        sku: 'COKE-500',
        price: 2.50,
        cost: 1.50,
        stock: 100,
        minStock: 20,
        categoryId: categories[0].id,
      },
    }),
    prisma.product.create({
      data: {
        name: 'Water Bottle 1L',
        description: 'Pure drinking water',
        barcode: '1234567890124',
        sku: 'WATER-1L',
        price: 1.00,
        cost: 0.50,
        stock: 200,
        minStock: 50,
        categoryId: categories[0].id,
      },
    }),
    // Snacks
    prisma.product.create({
      data: {
        name: 'Potato Chips',
        description: 'Crispy potato chips',
        barcode: '1234567890125',
        sku: 'CHIPS-REG',
        price: 3.00,
        cost: 1.80,
        stock: 75,
        minStock: 15,
        categoryId: categories[1].id,
      },
    }),
    prisma.product.create({
      data: {
        name: 'Chocolate Bar',
        description: 'Milk chocolate bar',
        barcode: '1234567890126',
        sku: 'CHOC-MILK',
        price: 2.00,
        cost: 1.20,
        stock: 150,
        minStock: 30,
        categoryId: categories[1].id,
      },
    }),
    // Dairy
    prisma.product.create({
      data: {
        name: 'Milk 1L',
        description: 'Fresh whole milk',
        barcode: '1234567890127',
        sku: 'MILK-1L',
        price: 3.50,
        cost: 2.50,
        stock: 50,
        minStock: 10,
        categoryId: categories[2].id,
      },
    }),
    // Household
    prisma.product.create({
      data: {
        name: 'Dish Soap',
        description: 'Liquid dish washing soap',
        barcode: '1234567890128',
        sku: 'SOAP-DISH',
        price: 4.00,
        cost: 2.80,
        stock: 30,
        minStock: 5,
        categoryId: categories[3].id,
      },
    }),
  ])

  console.log('✅ Products created')

  // Create a sample sale
  const sale = await prisma.sale.create({
    data: {
      total: 7.50,
      tax: 0.75,
      paymentType: 'CASH',
      status: 'COMPLETED',
      userId: cashierUser.id,
      saleItems: {
        create: [
          {
            quantity: 2,
            price: 2.50,
            total: 5.00,
            productId: products[0].id, // Coca Cola
          },
          {
            quantity: 1,
            price: 2.00,
            total: 2.00,
            productId: products[3].id, // Chocolate Bar
          },
        ],
      },
    },
  })

  console.log('✅ Sample sale created')

  console.log('🎉 Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
